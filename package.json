{"name": "deep-research", "description": "Use any LLMs (Large Language Models) for Deep Research. Support SSE API and MCP server.", "version": "0.9.16", "license": "MIT", "repository": {"url": "https://github.com/u14app/deep-research"}, "bugs": "https://github.com/u14app/deep-research/issues", "private": true, "scripts": {"dev": "next dev --turbopack -p 3002", "build": "next build", "build:standalone": "cross-env NEXT_PUBLIC_BUILD_MODE=standalone next build", "build:export": "cross-env NEXT_PUBLIC_BUILD_MODE=export next build", "start": "next start -p 3002", "lint": "next lint"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/azure": "^1.3.23", "@ai-sdk/deepseek": "^0.2.13", "@ai-sdk/google": "^1.2.14", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.21", "@ai-sdk/openai-compatible": "^0.2.14", "@ai-sdk/ui-utils": "^1.2.9", "@ai-sdk/xai": "^1.2.15", "@hookform/resolvers": "^4.1.2", "@openrouter/ai-sdk-provider": "^0.4.5", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@serwist/next": "^9.0.14", "@xiangfa/mdeditor": "^0.2.3", "@zip.js/zip.js": "^2.7.60", "ai": "^4.3.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "fuse.js": "^7.1.0", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-resources-to-backend": "^1.2.1", "katex": "^0.16.22", "localforage": "^1.10.0", "lucide-react": "^0.475.0", "marked": "^15.0.12", "mermaid": "^11.6.0", "nanoid": "^5.1.5", "next": "^15.3.1", "next-themes": "^0.4.4", "ollama-ai-provider": "^1.2.0", "p-limit": "^6.2.0", "pdfjs-dist": "5.1.91", "radash": "^12.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.1", "react-use-pwa-install": "^1.0.3", "react-zoom-pan-pinch": "^3.7.0", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "ts-md5": "^1.3.1", "unist-util-visit": "^5.0.0", "zod": "^3.24.2", "zod-to-json-schema": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.16", "@types/file-saver": "^2.0.7", "@types/hast": "^3.0.4", "@types/jsdom": "^21.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-plugin-react-compiler": "19.1.0-rc.1", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.1.7", "ignore-loader": "^0.1.2", "postcss": "^8", "serwist": "^9.0.14", "tailwindcss": "^3.4.1", "typescript": "^5"}, "engines": {"npm": ">= 9.8.0", "node": ">= 18.18.0"}}