"use client";

import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON>, User, <PERSON>h, Clock } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { useChapterStore } from "@/store/chapter";

interface ChapterDiscussionDialogProps {
  open: boolean;
  onClose: () => void;
  chapterId: string;
}

export default function ChapterDiscussionDialog({
  open,
  onClose,
  chapterId,
}: ChapterDiscussionDialogProps) {
  const { t } = useTranslation();
  const chapterStore = useChapterStore();

  const chapter = useMemo(() => {
    return chapterStore.getChapter(chapterId);
  }, [chapterStore, chapterId]);

  const discussions = useMemo(() => {
    return chapterStore.getChapterDiscussions(chapterId)
      .sort((a, b) => a.round - b.round);
  }, [chapterStore, chapterId]);

  const allKeywords = useMemo(() => {
    const keywords = discussions.flatMap(d => d.keywords);
    return [...new Set(keywords)];
  }, [discussions]);

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  if (!chapter) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            章节讨论记录 - {chapter.title}
          </DialogTitle>
          <DialogDescription>
            查看Alpha和Beta Agent的讨论过程和生成的关键词
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 章节信息 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">章节信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <span className="font-medium">目标：</span>
                <span className="text-muted-foreground ml-2">{chapter.goal}</span>
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>讨论轮数: {discussions.length}</span>
                <span>生成关键词: {allKeywords.length}</span>
              </div>
            </CardContent>
          </Card>

          {/* 关键词汇总 */}
          {allKeywords.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  生成的关键词
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {allKeywords.map((keyword, index) => (
                    <Badge key={index} variant="secondary">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 讨论记录 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">讨论记录</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[400px] px-6">
                {discussions.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    暂无讨论记录
                  </div>
                ) : (
                  <div className="space-y-6 pb-4">
                    {discussions.map((discussion, index) => (
                      <div key={discussion.id} className="space-y-4">
                        {/* 轮次标题 */}
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium flex items-center gap-2">
                            第{discussion.round}轮讨论
                          </h4>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {formatTimestamp(discussion.timestamp)}
                          </div>
                        </div>

                        {/* Alpha Agent发言 */}
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Bot className="h-4 w-4 text-blue-500" />
                            <span className="font-medium text-blue-500">Alpha Agent</span>
                            <Badge variant="outline" className="text-xs">研究策略</Badge>
                          </div>
                          <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border-l-4 border-blue-500">
                            <div className="text-sm whitespace-pre-wrap">
                              {discussion.alphaMessage}
                            </div>
                          </div>
                        </div>

                        {/* Beta Agent回应 */}
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Bot className="h-4 w-4 text-green-500" />
                            <span className="font-medium text-green-500">Beta Agent</span>
                            <Badge variant="outline" className="text-xs">评估优化</Badge>
                          </div>
                          <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg border-l-4 border-green-500">
                            <div className="text-sm whitespace-pre-wrap">
                              {discussion.betaMessage}
                            </div>
                          </div>
                        </div>

                        {/* 本轮关键词 */}
                        {discussion.keywords.length > 0 && (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Hash className="h-4 w-4 text-purple-500" />
                              <span className="font-medium text-purple-500">本轮关键词</span>
                            </div>
                            <div className="flex flex-wrap gap-2">
                              {discussion.keywords.map((keyword, keywordIndex) => (
                                <Badge 
                                  key={keywordIndex} 
                                  variant="outline"
                                  className="border-purple-200 text-purple-700"
                                >
                                  {keyword}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* 分隔线 */}
                        {index < discussions.length - 1 && (
                          <Separator className="my-6" />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
