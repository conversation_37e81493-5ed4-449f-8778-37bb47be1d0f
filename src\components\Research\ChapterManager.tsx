"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { 
  BookOpen, 
  MessageSquare, 
  Search, 
  FileText,
  Play,
  Pause,
  CheckCircle2,
  AlertCircle,
  LoaderCircle,
  Eye,
  Settings
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";

import { useChapterStore } from "@/store/chapter";
import { useTaskStore } from "@/store/task";
import useChapterResearch from "@/hooks/useChapterResearch";
import ChapterDiscussionDialog from "./ChapterDiscussionDialog";
import ChapterSearchResult from "./ChapterSearchResult";

interface ChapterManagerProps {
  onStartTraditionalResearch: () => void;
}

export default function ChapterManager({ onStartTraditionalResearch }: ChapterManagerProps) {
  const { t } = useTranslation();
  const chapterStore = useChapterStore();
  const taskStore = useTaskStore();
  const {
    executeChapterDiscussion,
    generateChapterReportForChapter,
    generateFinalChapterReport,
    getChapterDiscussionPreview,
    resetChapterResearch,
    status,
    isProcessing,
  } = useChapterResearch();
  
  const [selectedChapterId, setSelectedChapterId] = useState<string>("");
  const [showDiscussionDialog, setShowDiscussionDialog] = useState(false);
  const [showSearchResult, setShowSearchResult] = useState(false);

  const chapters = chapterStore.chapters.sort((a, b) => a.order - b.order);
  const completedChapters = chapters.filter(c => c.state === "completed").length;
  const chaptersWithReports = chapters.filter(c => c.chapterReport && c.chapterReport.trim()).length;
  const totalChapters = chapters.length;
  const progress = totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;
  const reportProgress = totalChapters > 0 ? (chaptersWithReports / totalChapters) * 100 : 0;

  const getChapterStateIcon = (state: Chapter["state"]) => {
    switch (state) {
      case "completed":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "processing":
        return <LoaderCircle className="h-4 w-4 animate-spin text-blue-500" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <BookOpen className="h-4 w-4 text-gray-400" />;
    }
  };

  const getChapterStateBadge = (state: Chapter["state"]) => {
    switch (state) {
      case "completed":
        return <Badge variant="default" className="bg-green-500">已完成</Badge>;
      case "processing":
        return <Badge variant="default" className="bg-blue-500">进行中</Badge>;
      case "failed":
        return <Badge variant="destructive">失败</Badge>;
      default:
        return <Badge variant="secondary">未开始</Badge>;
    }
  };

  const handleStartChapterDiscussion = async (chapterId: string) => {
    try {
      await executeChapterDiscussion(chapterId);
    } catch (error) {
      // 错误已在hook中处理
    }
  };

  const handleViewDiscussion = (chapterId: string) => {
    setSelectedChapterId(chapterId);
    setShowDiscussionDialog(true);
  };

  const handleViewSearchResult = (chapterId: string) => {
    setSelectedChapterId(chapterId);
    setShowSearchResult(true);
  };

  const handleGenerateChapterReport = async (chapterId: string) => {
    try {
      await generateChapterReportForChapter(chapterId);
    } catch (error) {
      // 错误已在hook中处理
    }
  };

  const handleGenerateFinalReport = async () => {
    try {
      await generateFinalChapterReport();
    } catch (error) {
      // 错误已在hook中处理
    }
  };

  const handleBackToTraditional = () => {
    resetChapterResearch();
    onStartTraditionalResearch();
  };

  if (chapters.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            暂无章节数据，请重新创建章节研究计划。
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 总体进度 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              章节式研究进度
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackToTraditional}
            >
              切换到传统模式
            </Button>
          </CardTitle>
          <CardDescription>
            共{totalChapters}个章节，已完成{completedChapters}个
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Progress value={progress} className="mb-2" />
          <div className="text-sm text-muted-foreground">
            进度: {completedChapters}/{totalChapters} ({Math.round(progress)}%)
          </div>
        </CardContent>
      </Card>

      {/* 当前状态 */}
      {status.step !== "idle" && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              {isProcessing && <LoaderCircle className="h-4 w-4 animate-spin" />}
              <span className="font-medium">
                {status.currentChapter && `章节: ${status.currentChapter}`}
                {status.currentRound && ` - 第${status.currentRound}轮`}
              </span>
            </div>
            {status.message && (
              <div className="text-sm text-muted-foreground mt-1">
                {status.message}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 报告生成进度 */}
      {chaptersWithReports > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                报告生成进度
              </span>
              <Button
                size="sm"
                onClick={handleGenerateFinalReport}
                disabled={isProcessing || chaptersWithReports < totalChapters}
              >
                {isProcessing && status.step === "finalizing" ? (
                  <>
                    <LoaderCircle className="h-4 w-4 animate-spin mr-2" />
                    生成中...
                  </>
                ) : (
                  "生成最终报告"
                )}
              </Button>
            </CardTitle>
            <CardDescription>
              共{totalChapters}个章节，已生成{chaptersWithReports}个章节报告
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Progress value={reportProgress} className="mb-2" />
            <div className="text-sm text-muted-foreground">
              报告进度: {chaptersWithReports}/{totalChapters} ({Math.round(reportProgress)}%)
            </div>
          </CardContent>
        </Card>
      )}

      {/* 章节列表 */}
      <Card>
        <CardHeader>
          <CardTitle>章节管理</CardTitle>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible className="w-full">
            {chapters.map((chapter) => {
              const discussions = chapterStore.getChapterDiscussions(chapter.id);
              const searchTasks = chapterStore.getChapterSearchTasks(chapter.id);
              
              return (
                <AccordionItem key={chapter.id} value={chapter.id}>
                  <AccordionTrigger>
                    <div className="flex items-center justify-between w-full mr-4">
                      <div className="flex items-center gap-3">
                        {getChapterStateIcon(chapter.state)}
                        <span className="font-medium">{chapter.order}. {chapter.title}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        {getChapterStateBadge(chapter.state)}
                        {discussions.length > 0 && (
                          <Badge variant="outline">
                            {discussions.length}轮讨论
                          </Badge>
                        )}
                        {searchTasks.length > 0 && (
                          <Badge variant="outline">
                            {searchTasks.length}个任务
                          </Badge>
                        )}
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-4">
                      {/* 章节目标 */}
                      <div>
                        <h5 className="font-medium mb-2">章节目标</h5>
                        <p className="text-sm text-muted-foreground bg-muted p-3 rounded">
                          {chapter.goal}
                        </p>
                      </div>

                      <Separator />

                      {/* 操作按钮 */}
                      <div className="flex gap-2">
                        {chapter.state === "unprocessed" && (
                          <Button
                            size="sm"
                            onClick={() => handleStartChapterDiscussion(chapter.id)}
                            disabled={isProcessing}
                          >
                            <Play className="h-4 w-4 mr-2" />
                            开始讨论
                          </Button>
                        )}
                        
                        {discussions.length > 0 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewDiscussion(chapter.id)}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            查看讨论
                          </Button>
                        )}
                        
                        {chapter.state === "completed" && searchTasks.length > 0 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewSearchResult(chapter.id)}
                          >
                            <Search className="h-4 w-4 mr-2" />
                            信息收集
                          </Button>
                        )}

                        {searchTasks.some(t => t.state === "completed") && !chapter.chapterReport && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleGenerateChapterReport(chapter.id)}
                            disabled={isProcessing}
                          >
                            <FileText className="h-4 w-4 mr-2" />
                            生成报告
                          </Button>
                        )}

                        {chapter.chapterReport && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // TODO: 查看章节报告
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            查看报告
                          </Button>
                        )}
                      </div>

                      {/* 统计信息 */}
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-center p-2 bg-muted rounded">
                          <div className="font-medium">{discussions.length}</div>
                          <div className="text-muted-foreground">讨论轮数</div>
                        </div>
                        <div className="text-center p-2 bg-muted rounded">
                          <div className="font-medium">{searchTasks.length}</div>
                          <div className="text-muted-foreground">搜索任务</div>
                        </div>
                        <div className="text-center p-2 bg-muted rounded">
                          <div className="font-medium">
                            {searchTasks.filter(t => t.state === "completed").length}
                          </div>
                          <div className="text-muted-foreground">已完成</div>
                        </div>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              );
            })}
          </Accordion>
        </CardContent>
      </Card>

      {/* 讨论对话框 */}
      <ChapterDiscussionDialog
        open={showDiscussionDialog}
        onClose={() => setShowDiscussionDialog(false)}
        chapterId={selectedChapterId}
      />

      {/* 搜索结果对话框 */}
      {showSearchResult && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg shadow-lg max-w-4xl max-h-[80vh] w-full mx-4 overflow-hidden">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="text-lg font-semibold">章节信息收集</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSearchResult(false)}
              >
                ✕
              </Button>
            </div>
            <div className="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
              <ChapterSearchResult chapterId={selectedChapterId} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
