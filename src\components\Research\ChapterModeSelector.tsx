"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { 
  BookOpen, 
  List, 
  Settings, 
  ChevronRight,
  LoaderCircle,
  AlertCircle,
  CheckCircle2
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

import { useTaskStore } from "@/store/task";
import useChapterResearch from "@/hooks/useChapterResearch";
import { generateChapterPreview } from "@/utils/chapter-parser";

const configSchema = z.object({
  maxDiscussionRounds: z.number().min(1).max(10),
  enableContextIsolation: z.boolean(),
  autoProgressToNextChapter: z.boolean(),
});

interface ChapterModeSelectorProps {
  onModeSelected: (mode: "traditional" | "chapter") => void;
}

export default function ChapterModeSelector({ onModeSelected }: ChapterModeSelectorProps) {
  const { t } = useTranslation();
  const taskStore = useTaskStore();
  const {
    parseResearchPlanToChapters,
    createChapterResearchPlan,
    status,
    isProcessing,
  } = useChapterResearch();
  
  const [selectedMode, setSelectedMode] = useState<"traditional" | "chapter" | null>(null);
  const [parsedChapters, setParsedChapters] = useState<any[]>([]);
  const [parseError, setParseError] = useState<string>("");
  
  const form = useForm<z.infer<typeof configSchema>>({
    resolver: zodResolver(configSchema),
    defaultValues: {
      maxDiscussionRounds: 3,
      enableContextIsolation: true,
      autoProgressToNextChapter: false,
    },
  });

  const handleModeSelect = (mode: "traditional" | "chapter") => {
    setSelectedMode(mode);
    if (mode === "traditional") {
      onModeSelected(mode);
    }
  };

  const handleParsePreview = async () => {
    try {
      setParseError("");
      const chapters = await parseResearchPlanToChapters(taskStore.reportPlan);
      setParsedChapters(chapters);
    } catch (error) {
      setParseError(error instanceof Error ? error.message : "解析失败");
      setParsedChapters([]);
    }
  };

  const handleCreateChapterPlan = async (values: z.infer<typeof configSchema>) => {
    try {
      await createChapterResearchPlan(taskStore.reportPlan, values);
      onModeSelected("chapter");
    } catch (error) {
      // 错误已在hook中处理
    }
  };

  if (!taskStore.reportPlan) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          请先生成研究计划，然后选择研究模式。
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">选择研究模式</h3>
        <p className="text-sm text-muted-foreground">
          选择传统模式或章节式研究模式来进行深度研究
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 传统模式 */}
        <Card 
          className={`cursor-pointer transition-all ${
            selectedMode === "traditional" ? "ring-2 ring-primary" : "hover:shadow-md"
          }`}
          onClick={() => handleModeSelect("traditional")}
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <List className="h-5 w-5" />
              传统研究模式
            </CardTitle>
            <CardDescription>
              基于研究计划直接生成关键词并进行信息收集
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                快速启动研究
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                适合简单主题
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                一次性完成
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 章节式模式 */}
        <Card 
          className={`cursor-pointer transition-all ${
            selectedMode === "chapter" ? "ring-2 ring-primary" : "hover:shadow-md"
          }`}
          onClick={() => handleModeSelect("chapter")}
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              章节式研究模式
            </CardTitle>
            <CardDescription>
              将研究计划解析为章节，每章节独立进行Agent讨论和信息收集
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                章节化管理
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                Agent智能讨论
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                上下文隔离
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                精细化控制
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 章节式模式配置 */}
      {selectedMode === "chapter" && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              章节式研究配置
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 解析预览 */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium">研究计划解析预览</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleParsePreview}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <LoaderCircle className="h-4 w-4 animate-spin" />
                  ) : (
                    "解析预览"
                  )}
                </Button>
              </div>
              
              {parseError && (
                <Alert className="mb-3">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{parseError}</AlertDescription>
                </Alert>
              )}
              
              {parsedChapters.length > 0 && (
                <div className="bg-muted p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary">
                      共{parsedChapters.length}个章节
                    </Badge>
                  </div>
                  <div className="text-sm whitespace-pre-line">
                    {generateChapterPreview(parsedChapters)}
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* 配置表单 */}
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleCreateChapterPlan)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="maxDiscussionRounds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>最大讨论轮数</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          max={10}
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex items-center justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setSelectedMode(null)}
                  >
                    返回选择
                  </Button>
                  <Button
                    type="submit"
                    disabled={isProcessing || parsedChapters.length === 0}
                  >
                    {isProcessing ? (
                      <>
                        <LoaderCircle className="h-4 w-4 animate-spin mr-2" />
                        创建中...
                      </>
                    ) : (
                      <>
                        创建章节研究计划
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
