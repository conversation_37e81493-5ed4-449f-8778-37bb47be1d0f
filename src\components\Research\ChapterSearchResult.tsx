"use client";

import { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { 
  Search, 
  CheckCircle2, 
  LoaderCircle, 
  AlertCircle,
  FileText,
  Eye,
  Play,
  Pause
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Separator } from "@/components/ui/separator";

import { useChapterStore } from "@/store/chapter";
import useDeepResearch from "@/hooks/useDeepResearch";

interface ChapterSearchResultProps {
  chapterId: string;
}

export default function ChapterSearchResult({ chapterId }: ChapterSearchResultProps) {
  const { t } = useTranslation();
  const chapterStore = useChapterStore();
  const { runSearchTask, status } = useDeepResearch();
  const [isSearching, setIsSearching] = useState(false);

  const chapter = useMemo(() => {
    return chapterStore.getChapter(chapterId);
  }, [chapterStore, chapterId]);

  const searchTasks = useMemo(() => {
    return chapterStore.getChapterSearchTasks(chapterId);
  }, [chapterStore, chapterId]);

  const completedTasks = useMemo(() => {
    return searchTasks.filter(task => task.state === "completed");
  }, [searchTasks]);

  const progress = searchTasks.length > 0 ? (completedTasks.length / searchTasks.length) * 100 : 0;

  const getTaskStateIcon = (state: SearchTask["state"]) => {
    switch (state) {
      case "completed":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "processing":
        return <LoaderCircle className="h-4 w-4 animate-spin text-blue-500" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Search className="h-4 w-4 text-gray-400" />;
    }
  };

  const handleStartSearch = async () => {
    if (!chapter || searchTasks.length === 0) return;
    
    try {
      setIsSearching(true);
      chapterStore.updateChapter(chapterId, { state: "processing" });
      
      // 执行搜索任务
      await runSearchTask(searchTasks);
      
      // 更新章节状态
      const allCompleted = searchTasks.every(task => task.state === "completed");
      chapterStore.updateChapter(chapterId, { 
        state: allCompleted ? "completed" : "processing" 
      });
      
    } catch (error) {
      chapterStore.updateChapter(chapterId, { state: "failed" });
    } finally {
      setIsSearching(false);
    }
  };

  const handleRetryTask = async (task: SearchTask) => {
    try {
      chapterStore.updateChapterSearchTask(chapterId, task.query, { state: "unprocessed" });
      await runSearchTask([task]);
    } catch (error) {
      // 错误已在hook中处理
    }
  };

  if (!chapter) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            章节不存在
          </div>
        </CardContent>
      </Card>
    );
  }

  if (searchTasks.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            该章节暂无搜索任务，请先完成Agent讨论
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* 章节信息收集进度 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            {chapter.title} - 信息收集
          </CardTitle>
          <CardDescription>
            共{searchTasks.length}个搜索任务，已完成{completedTasks.length}个
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Progress value={progress} className="mb-2" />
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">
              进度: {completedTasks.length}/{searchTasks.length} ({Math.round(progress)}%)
            </span>
            <Button
              size="sm"
              onClick={handleStartSearch}
              disabled={isSearching || progress === 100}
            >
              {isSearching ? (
                <>
                  <LoaderCircle className="h-4 w-4 animate-spin mr-2" />
                  搜索中...
                </>
              ) : progress === 100 ? (
                "已完成"
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  开始搜索
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 搜索任务列表 */}
      <Card>
        <CardHeader>
          <CardTitle>搜索任务</CardTitle>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible className="w-full">
            {searchTasks.map((task, index) => (
              <AccordionItem key={task.query} value={task.query}>
                <AccordionTrigger>
                  <div className="flex items-center justify-between w-full mr-4">
                    <div className="flex items-center gap-3">
                      {getTaskStateIcon(task.state)}
                      <span className="font-medium">{task.query}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={task.state === "completed" ? "default" : "secondary"}>
                        {task.state === "completed" ? "已完成" : 
                         task.state === "processing" ? "进行中" :
                         task.state === "failed" ? "失败" : "未开始"}
                      </Badge>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4">
                    {/* 研究目标 */}
                    <div>
                      <h5 className="font-medium mb-2">研究目标</h5>
                      <p className="text-sm text-muted-foreground bg-muted p-3 rounded">
                        {task.researchGoal}
                      </p>
                    </div>

                    {/* 学习内容 */}
                    {task.learning && (
                      <>
                        <Separator />
                        <div>
                          <h5 className="font-medium mb-2">学习内容</h5>
                          <div className="text-sm bg-muted p-3 rounded max-h-40 overflow-y-auto">
                            {task.learning}
                          </div>
                        </div>
                      </>
                    )}

                    {/* 来源信息 */}
                    {task.sources && task.sources.length > 0 && (
                      <>
                        <Separator />
                        <div>
                          <h5 className="font-medium mb-2">信息来源</h5>
                          <div className="space-y-2">
                            {task.sources.slice(0, 3).map((source, sourceIndex) => (
                              <div key={sourceIndex} className="text-sm">
                                <a 
                                  href={source.url} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-blue-500 hover:underline"
                                >
                                  {source.title || source.url}
                                </a>
                              </div>
                            ))}
                            {task.sources.length > 3 && (
                              <div className="text-sm text-muted-foreground">
                                还有{task.sources.length - 3}个来源...
                              </div>
                            )}
                          </div>
                        </div>
                      </>
                    )}

                    {/* 操作按钮 */}
                    <Separator />
                    <div className="flex gap-2">
                      {task.state === "failed" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRetryTask(task)}
                        >
                          重试
                        </Button>
                      )}
                      {task.learning && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            // TODO: 添加到知识库
                          }}
                        >
                          <FileText className="h-4 w-4 mr-2" />
                          添加到知识库
                        </Button>
                      )}
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>
    </div>
  );
}
