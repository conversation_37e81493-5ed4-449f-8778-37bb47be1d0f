/**
 * 章节式研究Hook
 * 管理章节式研究的整个流程
 */

import { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { customAlphabet } from "nanoid";

import { useTaskStore } from "@/store/task";
import { useChapterStore } from "@/store/chapter";
import { parseResearchPlan, validateChapters, type ParsedChapter } from "@/utils/chapter-parser";
import { executeAgentDiscussion, generateSearchTasksFromKeywords, type AgentDiscussionContext } from "@/utils/chapter-agent";
import { generateChapterReport, generateFinalReport } from "@/utils/chapter-report";
import { createModelProvider } from "@/utils/model";
import { useSettingStore } from "@/store/setting";

const nanoid = customAlphabet("1234567890abcdefghijklmnopqrstuvwxyz", 12);

export interface ChapterResearchStatus {
  step: "idle" | "parsing" | "discussing" | "searching" | "writing" | "finalizing" | "completed";
  currentChapter?: string;
  currentRound?: number;
  message?: string;
}

export default function useChapterResearch() {
  const { t } = useTranslation();
  const [status, setStatus] = useState<ChapterResearchStatus>({ step: "idle" });
  const [isProcessing, setIsProcessing] = useState(false);
  
  const taskStore = useTaskStore();
  const chapterStore = useChapterStore();

  /**
   * 解析研究计划为章节
   */
  const parseResearchPlanToChapters = useCallback(async (planText: string) => {
    setStatus({ step: "parsing", message: "正在解析研究计划..." });
    
    try {
      const parseResult = parseResearchPlan(planText);
      
      if (!parseResult.success) {
        throw new Error(parseResult.error || "解析失败");
      }
      
      const validation = validateChapters(parseResult.chapters);
      if (!validation.valid) {
        throw new Error(`章节验证失败: ${validation.errors.join(", ")}`);
      }
      
      return parseResult.chapters;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "解析研究计划失败";
      toast.error(errorMessage);
      setStatus({ step: "idle" });
      throw error;
    }
  }, []);

  /**
   * 创建章节式研究计划
   */
  const createChapterResearchPlan = useCallback(async (
    planText: string,
    config: ChapterResearchConfig = {
      maxDiscussionRounds: 3,
      enableContextIsolation: true,
      autoProgressToNextChapter: false,
    }
  ) => {
    try {
      setIsProcessing(true);
      
      // 解析章节
      const parsedChapters = await parseResearchPlanToChapters(planText);
      
      // 清空现有章节数据
      chapterStore.clear();
      
      // 创建章节
      const chapterIds: string[] = [];
      for (const parsedChapter of parsedChapters) {
        const chapterId = chapterStore.addChapter({
          title: parsedChapter.title,
          goal: parsedChapter.goal,
          order: parsedChapter.order,
          state: "unprocessed",
          agentDiscussions: [],
          searchTasks: [],
          chapterReport: "",
        });
        chapterIds.push(chapterId);
      }
      
      // 创建章节研究计划
      const chapterResearchPlan: ChapterResearchPlan = {
        id: nanoid(),
        originalPlan: planText,
        chapters: chapterStore.chapters,
        config,
        mode: "chapter",
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };
      
      // 更新TaskStore
      taskStore.setResearchMode("chapter");
      taskStore.setChapterResearchPlan(chapterResearchPlan);
      
      // 设置第一个章节为当前章节
      if (chapterIds.length > 0) {
        chapterStore.setCurrentChapter(chapterIds[0]);
      }
      
      setStatus({ step: "completed", message: `成功创建${parsedChapters.length}个章节` });
      toast.success(`章节式研究计划创建成功，共${parsedChapters.length}个章节`);
      
      return chapterResearchPlan;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "创建章节研究计划失败";
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [chapterStore, taskStore, parseResearchPlanToChapters]);

  /**
   * 执行章节Agent讨论
   */
  const executeChapterDiscussion = useCallback(async (chapterId: string) => {
    try {
      setIsProcessing(true);
      const chapter = chapterStore.getChapter(chapterId);
      if (!chapter) {
        throw new Error("章节不存在");
      }
      
      const { thinkingModel } = useSettingStore.getState();
      const modelProvider = await createModelProvider(thinkingModel);
      
      const config = taskStore.chapterResearchPlan?.config || {
        maxDiscussionRounds: 3,
        enableContextIsolation: true,
        autoProgressToNextChapter: false,
      };
      
      let currentRound = 1;
      const existingDiscussions = chapterStore.getChapterDiscussions(chapterId);
      if (existingDiscussions.length > 0) {
        currentRound = Math.max(...existingDiscussions.map(d => d.round)) + 1;
      }
      
      // 更新章节状态
      chapterStore.updateChapter(chapterId, { state: "processing" });
      
      while (currentRound <= config.maxDiscussionRounds) {
        setStatus({
          step: "discussing",
          currentChapter: chapter.title,
          currentRound,
          message: `第${currentRound}轮讨论中...`,
        });
        
        const context: AgentDiscussionContext = {
          chapterTitle: chapter.title,
          chapterGoal: chapter.goal,
          previousDiscussions: chapterStore.getChapterDiscussions(chapterId),
          previousSearchResults: chapterStore.getChapterSearchTasks(chapterId),
          round: currentRound,
          maxRounds: config.maxDiscussionRounds,
        };
        
        const discussionResult = await executeAgentDiscussion(context, modelProvider);
        
        // 保存讨论结果
        chapterStore.addAgentDiscussion({
          chapterId,
          round: currentRound,
          alphaMessage: discussionResult.alphaMessage,
          betaMessage: discussionResult.betaMessage,
          keywords: discussionResult.keywords,
        });
        
        // 如果Agent认为不需要继续讨论，跳出循环
        if (!discussionResult.shouldContinue) {
          break;
        }
        
        currentRound++;
      }
      
      // 生成搜索任务
      const allDiscussions = chapterStore.getChapterDiscussions(chapterId);
      const allKeywords = allDiscussions.flatMap(d => d.keywords);
      const uniqueKeywords = [...new Set(allKeywords)];
      
      const searchTasks = generateSearchTasksFromKeywords(
        uniqueKeywords,
        chapter.title,
        chapter.goal
      );
      
      // 添加搜索任务到章节
      for (const task of searchTasks) {
        chapterStore.addChapterSearchTask(chapterId, task);
      }
      
      chapterStore.updateChapter(chapterId, { state: "completed" });
      setStatus({ 
        step: "completed", 
        message: `章节"${chapter.title}"讨论完成，生成${searchTasks.length}个搜索任务` 
      });
      
      toast.success(`章节"${chapter.title}"Agent讨论完成`);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "章节讨论失败";
      toast.error(errorMessage);
      
      // 更新章节状态为失败
      chapterStore.updateChapter(chapterId, { state: "failed" });
      setStatus({ step: "idle" });
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [chapterStore, taskStore]);

  /**
   * 获取章节讨论预览
   */
  const getChapterDiscussionPreview = useCallback((chapterId: string) => {
    const discussions = chapterStore.getChapterDiscussions(chapterId);
    if (discussions.length === 0) {
      return "暂无讨论记录";
    }
    
    return discussions
      .sort((a, b) => a.round - b.round)
      .map(discussion => {
        return `**第${discussion.round}轮讨论**\n\n**Alpha:** ${discussion.alphaMessage.substring(0, 100)}...\n\n**Beta:** ${discussion.betaMessage.substring(0, 100)}...\n\n**关键词:** ${discussion.keywords.join(", ")}`;
      })
      .join("\n\n---\n\n");
  }, [chapterStore]);

  /**
   * 生成章节报告
   */
  const generateChapterReportForChapter = useCallback(async (chapterId: string) => {
    try {
      setIsProcessing(true);
      const chapter = chapterStore.getChapter(chapterId);
      if (!chapter) {
        throw new Error("章节不存在");
      }

      setStatus({
        step: "writing",
        currentChapter: chapter.title,
        message: "正在生成章节报告...",
      });

      const { thinkingModel } = useSettingStore.getState();
      const modelProvider = await createModelProvider(thinkingModel);

      const searchResults = chapterStore.getChapterSearchTasks(chapterId);
      const discussions = chapterStore.getChapterDiscussions(chapterId);

      const report = await generateChapterReport(
        chapter,
        searchResults,
        discussions,
        modelProvider
      );

      chapterStore.updateChapter(chapterId, { chapterReport: report });

      setStatus({
        step: "completed",
        message: `章节"${chapter.title}"报告生成完成`,
      });

      toast.success(`章节"${chapter.title}"报告生成完成`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "生成章节报告失败";
      toast.error(errorMessage);
      setStatus({ step: "idle" });
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [chapterStore]);

  /**
   * 生成最终合成报告
   */
  const generateFinalChapterReport = useCallback(async (requirement?: string) => {
    try {
      setIsProcessing(true);

      setStatus({
        step: "finalizing",
        message: "正在合成最终报告...",
      });

      const { thinkingModel } = useSettingStore.getState();
      const modelProvider = await createModelProvider(thinkingModel);

      const plan = taskStore.chapterResearchPlan;
      if (!plan) {
        throw new Error("章节研究计划不存在");
      }

      const finalReport = await generateFinalReport(
        plan.originalPlan,
        chapterStore.chapters,
        modelProvider,
        requirement
      );

      taskStore.updateFinalReport(finalReport);

      setStatus({
        step: "completed",
        message: "最终报告生成完成",
      });

      toast.success("最终报告生成完成");

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "生成最终报告失败";
      toast.error(errorMessage);
      setStatus({ step: "idle" });
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [chapterStore, taskStore]);

  /**
   * 重置章节研究状态
   */
  const resetChapterResearch = useCallback(() => {
    chapterStore.clear();
    taskStore.setResearchMode("traditional");
    taskStore.setChapterResearchPlan(undefined);
    setStatus({ step: "idle" });
    setIsProcessing(false);
  }, [chapterStore, taskStore]);

  return {
    // 状态
    status,
    isProcessing,
    
    // 方法
    parseResearchPlanToChapters,
    createChapterResearchPlan,
    executeChapterDiscussion,
    generateChapterReportForChapter,
    generateFinalChapterReport,
    getChapterDiscussionPreview,
    resetChapterResearch,
    
    // Store数据
    chapters: chapterStore.chapters,
    currentChapterId: chapterStore.currentChapterId,
    researchMode: taskStore.researchMode,
    chapterResearchPlan: taskStore.chapterResearchPlan,
  };
}
