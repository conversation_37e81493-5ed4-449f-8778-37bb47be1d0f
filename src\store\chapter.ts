import { create } from "zustand";
import { persist, type StorageValue } from "zustand/middleware";
import { researchStore } from "@/utils/storage";
import { clone, pick } from "radash";
import { customAlphabet } from "nanoid";

const nanoid = customAlphabet("1234567890abcdefghijklmnopqrstuvwxyz", 12);

export interface ChapterStore {
  chapters: Chapter[];
  currentChapterId: string;
  agentDiscussions: AgentDiscussion[];
}

interface ChapterFunction {
  // 章节管理
  addChapter: (chapter: Omit<Chapter, "id" | "createdAt" | "updatedAt">) => string;
  updateChapter: (id: string, chapter: Partial<Chapter>) => boolean;
  removeChapter: (id: string) => boolean;
  getChapter: (id: string) => Chapter | null;
  setCurrentChapter: (id: string) => void;
  reorderChapters: (chapters: Chapter[]) => void;
  
  // Agent讨论管理
  addAgentDiscussion: (discussion: Omit<AgentDiscussion, "id" | "timestamp">) => string;
  updateAgentDiscussion: (id: string, discussion: Partial<AgentDiscussion>) => boolean;
  removeAgentDiscussion: (id: string) => boolean;
  getChapterDiscussions: (chapterId: string) => AgentDiscussion[];
  clearChapterDiscussions: (chapterId: string) => void;
  
  // 章节搜索任务管理
  addChapterSearchTask: (chapterId: string, task: SearchTask) => boolean;
  updateChapterSearchTask: (chapterId: string, taskQuery: string, task: Partial<SearchTask>) => boolean;
  removeChapterSearchTask: (chapterId: string, taskQuery: string) => boolean;
  getChapterSearchTasks: (chapterId: string) => SearchTask[];
  
  // 工具方法
  clear: () => void;
  reset: () => void;
}

const defaultValues: ChapterStore = {
  chapters: [],
  currentChapterId: "",
  agentDiscussions: [],
};

export const useChapterStore = create(
  persist<ChapterStore & ChapterFunction>(
    (set, get) => ({
      ...defaultValues,
      
      // 章节管理实现
      addChapter: (chapterData) => {
        const id = nanoid();
        const currentTime = Date.now();
        const newChapter: Chapter = {
          ...chapterData,
          id,
          state: "unprocessed",
          agentDiscussions: [],
          searchTasks: [],
          chapterReport: "",
          createdAt: currentTime,
          updatedAt: currentTime,
        };
        set((state) => ({
          chapters: [...state.chapters, newChapter],
        }));
        return id;
      },
      
      updateChapter: (id, chapterData) => {
        const chapters = get().chapters.map((chapter) => {
          if (chapter.id === id) {
            return {
              ...chapter,
              ...chapterData,
              updatedAt: Date.now(),
            };
          }
          return chapter;
        });
        set(() => ({ chapters }));
        return true;
      },
      
      removeChapter: (id) => {
        set((state) => ({
          chapters: state.chapters.filter((chapter) => chapter.id !== id),
          agentDiscussions: state.agentDiscussions.filter((discussion) => discussion.chapterId !== id),
        }));
        return true;
      },
      
      getChapter: (id) => {
        const chapter = get().chapters.find((chapter) => chapter.id === id);
        return chapter ? clone(chapter) : null;
      },
      
      setCurrentChapter: (id) => {
        set(() => ({ currentChapterId: id }));
      },
      
      reorderChapters: (chapters) => {
        set(() => ({ chapters: [...chapters] }));
      },
      
      // Agent讨论管理实现
      addAgentDiscussion: (discussionData) => {
        const id = nanoid();
        const newDiscussion: AgentDiscussion = {
          ...discussionData,
          id,
          timestamp: Date.now(),
        };
        set((state) => ({
          agentDiscussions: [...state.agentDiscussions, newDiscussion],
        }));
        return id;
      },
      
      updateAgentDiscussion: (id, discussionData) => {
        const agentDiscussions = get().agentDiscussions.map((discussion) => {
          if (discussion.id === id) {
            return {
              ...discussion,
              ...discussionData,
            };
          }
          return discussion;
        });
        set(() => ({ agentDiscussions }));
        return true;
      },
      
      removeAgentDiscussion: (id) => {
        set((state) => ({
          agentDiscussions: state.agentDiscussions.filter((discussion) => discussion.id !== id),
        }));
        return true;
      },
      
      getChapterDiscussions: (chapterId) => {
        return get().agentDiscussions.filter((discussion) => discussion.chapterId === chapterId);
      },
      
      clearChapterDiscussions: (chapterId) => {
        set((state) => ({
          agentDiscussions: state.agentDiscussions.filter((discussion) => discussion.chapterId !== chapterId),
        }));
      },
      
      // 章节搜索任务管理实现
      addChapterSearchTask: (chapterId, task) => {
        const chapters = get().chapters.map((chapter) => {
          if (chapter.id === chapterId) {
            return {
              ...chapter,
              searchTasks: [...chapter.searchTasks, task],
              updatedAt: Date.now(),
            };
          }
          return chapter;
        });
        set(() => ({ chapters }));
        return true;
      },
      
      updateChapterSearchTask: (chapterId, taskQuery, taskData) => {
        const chapters = get().chapters.map((chapter) => {
          if (chapter.id === chapterId) {
            const searchTasks = chapter.searchTasks.map((task) => {
              if (task.query === taskQuery) {
                return { ...task, ...taskData };
              }
              return task;
            });
            return {
              ...chapter,
              searchTasks,
              updatedAt: Date.now(),
            };
          }
          return chapter;
        });
        set(() => ({ chapters }));
        return true;
      },
      
      removeChapterSearchTask: (chapterId, taskQuery) => {
        const chapters = get().chapters.map((chapter) => {
          if (chapter.id === chapterId) {
            return {
              ...chapter,
              searchTasks: chapter.searchTasks.filter((task) => task.query !== taskQuery),
              updatedAt: Date.now(),
            };
          }
          return chapter;
        });
        set(() => ({ chapters }));
        return true;
      },
      
      getChapterSearchTasks: (chapterId) => {
        const chapter = get().chapters.find((chapter) => chapter.id === chapterId);
        return chapter ? chapter.searchTasks : [];
      },
      
      // 工具方法
      clear: () => set(() => ({ chapters: [], agentDiscussions: [] })),
      reset: () => set(() => ({ ...defaultValues })),
    }),
    {
      name: "chapterStore",
      version: 1,
      storage: {
        getItem: async (key: string) => {
          return await researchStore.getItem<
            StorageValue<ChapterStore & ChapterFunction>
          >(key);
        },
        setItem: async (
          key: string,
          store: StorageValue<ChapterStore & ChapterFunction>
        ) => {
          return await researchStore.setItem(key, {
            state: pick(store.state, ["chapters", "currentChapterId", "agentDiscussions"]),
            version: store.version,
          });
        },
        removeItem: async (key: string) => await researchStore.removeItem(key),
      },
    }
  )
);
