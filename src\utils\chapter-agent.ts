/**
 * 章节Agent讨论系统
 * 实现Alpha和Beta Agent的对话机制
 */

import { streamText } from "ai";
import { createModelProvider } from "@/utils/model";

export interface AgentDiscussionResult {
  alphaMessage: string;
  betaMessage: string;
  keywords: string[];
  shouldContinue: boolean;
}

export interface AgentDiscussionContext {
  chapterTitle: string;
  chapterGoal: string;
  previousDiscussions: AgentDiscussion[];
  previousSearchResults?: SearchTask[];
  round: number;
  maxRounds: number;
}

/**
 * Alpha Agent提示词 - 负责提出研究方向和关键词
 */
function getAlphaAgentPrompt(context: AgentDiscussionContext): string {
  const { chapterTitle, chapterGoal, previousDiscussions, previousSearchResults, round, maxRounds } = context;
  
  let prompt = `你是Alpha研究员，负责为章节"${chapterTitle}"制定研究策略。

章节目标：${chapterGoal}

当前是第${round}轮讨论（共${maxRounds}轮）。

你的任务：
1. 分析章节目标，提出具体的研究方向
2. 基于研究方向生成精确的搜索关键词
3. 考虑信息收集的优先级和策略

`;

  if (previousDiscussions.length > 0) {
    prompt += `\n之前的讨论历史：\n`;
    previousDiscussions.forEach((discussion, index) => {
      prompt += `第${discussion.round}轮：\n`;
      prompt += `Alpha: ${discussion.alphaMessage}\n`;
      prompt += `Beta: ${discussion.betaMessage}\n`;
      prompt += `关键词: ${discussion.keywords.join(', ')}\n\n`;
    });
  }

  if (previousSearchResults && previousSearchResults.length > 0) {
    prompt += `\n已收集的信息：\n`;
    previousSearchResults.forEach((result, index) => {
      prompt += `${index + 1}. 查询: ${result.query}\n`;
      prompt += `   状态: ${result.state}\n`;
      if (result.learning) {
        prompt += `   学习内容: ${result.learning.substring(0, 200)}...\n`;
      }
      prompt += `\n`;
    });
  }

  prompt += `\n请提出你的研究建议，包括：
1. 具体的研究方向和策略
2. 建议的搜索关键词（3-5个）
3. 对当前信息收集状况的评估

请用中文回复，保持专业和建设性的语调。`;

  return prompt;
}

/**
 * Beta Agent提示词 - 负责评估和优化Alpha的建议
 */
function getBetaAgentPrompt(context: AgentDiscussionContext, alphaMessage: string): string {
  const { chapterTitle, chapterGoal, previousDiscussions, previousSearchResults, round, maxRounds } = context;
  
  let prompt = `你是Beta研究员，负责评估和优化Alpha研究员的建议。

章节目标：${chapterGoal}
当前是第${round}轮讨论（共${maxRounds}轮）。

Alpha研究员的建议：
${alphaMessage}

你的任务：
1. 评估Alpha的研究方向是否合理
2. 优化或补充搜索关键词
3. 指出可能遗漏的重要方面
4. 判断是否需要继续讨论

`;

  if (previousSearchResults && previousSearchResults.length > 0) {
    prompt += `\n当前已有的搜索结果：\n`;
    previousSearchResults.forEach((result, index) => {
      prompt += `${index + 1}. ${result.query} (${result.state})\n`;
    });
  }

  prompt += `\n请提供你的评估和建议：
1. 对Alpha建议的评价
2. 优化后的关键词列表
3. 补充的研究方向（如有）
4. 是否建议继续讨论还是开始信息收集

请用中文回复，保持客观和建设性的态度。`;

  return prompt;
}

/**
 * 提取关键词的工具函数
 */
function extractKeywords(text: string): string[] {
  // 简单的关键词提取逻辑，可以后续优化
  const keywordPatterns = [
    /关键词[：:]\s*([^\n]+)/gi,
    /搜索词[：:]\s*([^\n]+)/gi,
    /查询[：:]\s*([^\n]+)/gi,
    /建议搜索[：:]\s*([^\n]+)/gi,
  ];
  
  const keywords: string[] = [];
  
  for (const pattern of keywordPatterns) {
    const matches = text.matchAll(pattern);
    for (const match of matches) {
      if (match[1]) {
        const extractedKeywords = match[1]
          .split(/[,，、；;]/)
          .map(k => k.trim())
          .filter(k => k.length > 0);
        keywords.push(...extractedKeywords);
      }
    }
  }
  
  // 如果没有找到明确的关键词标记，尝试从文本中提取
  if (keywords.length === 0) {
    const sentences = text.split(/[。！？.!?]/).filter(s => s.trim());
    for (const sentence of sentences) {
      if (sentence.includes('搜索') || sentence.includes('查询') || sentence.includes('关键词')) {
        const words = sentence.split(/[\s,，、；;]/).filter(w => w.trim().length > 1);
        keywords.push(...words.slice(0, 3)); // 取前3个词
      }
    }
  }
  
  // 去重并限制数量
  return [...new Set(keywords)].slice(0, 8);
}

/**
 * 判断是否应该继续讨论
 */
function shouldContinueDiscussion(
  betaMessage: string, 
  round: number, 
  maxRounds: number,
  previousKeywords: string[]
): boolean {
  // 如果已达到最大轮数，停止讨论
  if (round >= maxRounds) {
    return false;
  }
  
  // 检查Beta是否明确表示可以开始收集信息
  const stopIndicators = [
    '开始信息收集',
    '开始搜索',
    '可以开始',
    '建议开始',
    '不需要继续',
    '足够了',
    '可以进行',
  ];
  
  const shouldStop = stopIndicators.some(indicator => 
    betaMessage.includes(indicator)
  );
  
  if (shouldStop) {
    return false;
  }
  
  // 如果Beta提出了新的建议或关键词，继续讨论
  const continueIndicators = [
    '建议',
    '补充',
    '还需要',
    '应该考虑',
    '可以增加',
    '进一步',
  ];
  
  const shouldContinue = continueIndicators.some(indicator => 
    betaMessage.includes(indicator)
  );
  
  return shouldContinue;
}

/**
 * 执行Agent讨论
 */
export async function executeAgentDiscussion(
  context: AgentDiscussionContext,
  modelProvider: any
): Promise<AgentDiscussionResult> {
  try {
    // Alpha Agent发言
    const alphaPrompt = getAlphaAgentPrompt(context);
    const alphaResult = await streamText({
      model: modelProvider,
      prompt: alphaPrompt,
    });
    
    let alphaMessage = '';
    for await (const chunk of alphaResult.textStream) {
      alphaMessage += chunk;
    }
    
    // Beta Agent回应
    const betaPrompt = getBetaAgentPrompt(context, alphaMessage);
    const betaResult = await streamText({
      model: modelProvider,
      prompt: betaPrompt,
    });
    
    let betaMessage = '';
    for await (const chunk of betaResult.textStream) {
      betaMessage += chunk;
    }
    
    // 提取关键词
    const alphaKeywords = extractKeywords(alphaMessage);
    const betaKeywords = extractKeywords(betaMessage);
    const allKeywords = [...alphaKeywords, ...betaKeywords];
    const uniqueKeywords = [...new Set(allKeywords)];
    
    // 判断是否继续讨论
    const shouldContinue = shouldContinueDiscussion(
      betaMessage,
      context.round,
      context.maxRounds,
      uniqueKeywords
    );
    
    return {
      alphaMessage,
      betaMessage,
      keywords: uniqueKeywords,
      shouldContinue,
    };
  } catch (error) {
    throw new Error(`Agent讨论执行失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 生成搜索任务
 */
export function generateSearchTasksFromKeywords(
  keywords: string[],
  chapterTitle: string,
  chapterGoal: string
): SearchTask[] {
  return keywords.map(keyword => ({
    state: "unprocessed" as const,
    query: keyword,
    researchGoal: `为章节"${chapterTitle}"收集关于"${keyword}"的相关信息，目标：${chapterGoal}`,
    learning: "",
    sources: [],
    images: [],
  }));
}
