/**
 * 章节解析工具
 * 用于将研究计划解析为章节结构
 */

export interface ParsedChapter {
  title: string;
  goal: string;
  order: number;
  content?: string;
}

export interface ChapterParseResult {
  success: boolean;
  chapters: ParsedChapter[];
  error?: string;
}

/**
 * 解析Markdown格式的研究计划
 * 支持 # ## ### 标题层级
 */
export function parseMarkdownPlan(planText: string): ChapterParseResult {
  try {
    const lines = planText.split('\n').filter(line => line.trim());
    const chapters: ParsedChapter[] = [];
    let currentChapter: Partial<ParsedChapter> | null = null;
    let order = 1;

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 检测标题行 (# ## ###)
      const headerMatch = trimmedLine.match(/^(#{1,3})\s+(.+)$/);
      if (headerMatch) {
        // 保存之前的章节
        if (currentChapter && currentChapter.title) {
          chapters.push({
            title: currentChapter.title,
            goal: currentChapter.goal || currentChapter.title,
            order: order++,
            content: currentChapter.content,
          });
        }
        
        // 开始新章节
        currentChapter = {
          title: headerMatch[2].trim(),
          content: '',
        };
      } else if (currentChapter) {
        // 累积章节内容
        if (currentChapter.content) {
          currentChapter.content += '\n' + trimmedLine;
        } else {
          currentChapter.content = trimmedLine;
        }
        
        // 如果还没有设置goal，使用第一行非空内容作为goal
        if (!currentChapter.goal && trimmedLine) {
          currentChapter.goal = trimmedLine;
        }
      }
    }
    
    // 保存最后一个章节
    if (currentChapter && currentChapter.title) {
      chapters.push({
        title: currentChapter.title,
        goal: currentChapter.goal || currentChapter.title,
        order: order++,
        content: currentChapter.content,
      });
    }

    if (chapters.length === 0) {
      return {
        success: false,
        chapters: [],
        error: "未能解析出任何章节，请检查研究计划格式",
      };
    }

    return {
      success: true,
      chapters,
    };
  } catch (error) {
    return {
      success: false,
      chapters: [],
      error: error instanceof Error ? error.message : "解析失败",
    };
  }
}

/**
 * 解析JSON格式的研究计划
 */
export function parseJsonPlan(planText: string): ChapterParseResult {
  try {
    const planData = JSON.parse(planText);
    
    if (!planData.chapters || !Array.isArray(planData.chapters)) {
      return {
        success: false,
        chapters: [],
        error: "JSON格式错误：缺少chapters数组",
      };
    }

    const chapters: ParsedChapter[] = planData.chapters.map((chapter: any, index: number) => ({
      title: chapter.title || `章节 ${index + 1}`,
      goal: chapter.goal || chapter.description || chapter.title || `章节 ${index + 1} 目标`,
      order: chapter.order || index + 1,
      content: chapter.content || '',
    }));

    return {
      success: true,
      chapters,
    };
  } catch (error) {
    return {
      success: false,
      chapters: [],
      error: error instanceof Error ? error.message : "JSON解析失败",
    };
  }
}

/**
 * 自动检测并解析研究计划
 */
export function parseResearchPlan(planText: string): ChapterParseResult {
  if (!planText || !planText.trim()) {
    return {
      success: false,
      chapters: [],
      error: "研究计划内容为空",
    };
  }

  const trimmedPlan = planText.trim();
  
  // 尝试JSON格式
  if (trimmedPlan.startsWith('{') || trimmedPlan.startsWith('[')) {
    const jsonResult = parseJsonPlan(trimmedPlan);
    if (jsonResult.success) {
      return jsonResult;
    }
  }
  
  // 尝试Markdown格式
  const markdownResult = parseMarkdownPlan(trimmedPlan);
  if (markdownResult.success) {
    return markdownResult;
  }
  
  // 如果都失败了，尝试将整个内容作为单个章节
  return {
    success: true,
    chapters: [{
      title: "研究报告",
      goal: "完成整体研究目标",
      order: 1,
      content: trimmedPlan,
    }],
  };
}

/**
 * 验证章节数据
 */
export function validateChapters(chapters: ParsedChapter[]): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (chapters.length === 0) {
    errors.push("章节列表为空");
  }
  
  chapters.forEach((chapter, index) => {
    if (!chapter.title || !chapter.title.trim()) {
      errors.push(`第${index + 1}个章节缺少标题`);
    }
    
    if (!chapter.goal || !chapter.goal.trim()) {
      errors.push(`第${index + 1}个章节缺少目标`);
    }
    
    if (typeof chapter.order !== 'number' || chapter.order < 1) {
      errors.push(`第${index + 1}个章节的顺序号无效`);
    }
  });
  
  // 检查顺序号是否重复
  const orders = chapters.map(c => c.order);
  const uniqueOrders = new Set(orders);
  if (orders.length !== uniqueOrders.size) {
    errors.push("章节顺序号存在重复");
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * 生成章节预览文本
 */
export function generateChapterPreview(chapters: ParsedChapter[]): string {
  if (chapters.length === 0) {
    return "暂无章节";
  }
  
  return chapters
    .sort((a, b) => a.order - b.order)
    .map((chapter, index) => {
      return `${index + 1}. **${chapter.title}**\n   目标：${chapter.goal}`;
    })
    .join('\n\n');
}
