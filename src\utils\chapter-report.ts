/**
 * 章节报告生成工具
 * 用于生成章节小报告和最终合成报告
 */

import { streamText } from "ai";

/**
 * 生成章节报告的提示词
 */
function getChapterReportPrompt(
  chapterTitle: string,
  chapterGoal: string,
  searchResults: SearchTask[],
  discussions: AgentDiscussion[]
): string {
  let prompt = `你是一名专业的研究报告撰写员，需要为章节"${chapterTitle}"撰写一份详细的小报告。

章节目标：${chapterGoal}

`;

  // 添加Agent讨论内容
  if (discussions.length > 0) {
    prompt += `## Agent讨论过程\n\n`;
    discussions.forEach((discussion, index) => {
      prompt += `### 第${discussion.round}轮讨论\n\n`;
      prompt += `**Alpha Agent建议：**\n${discussion.alphaMessage}\n\n`;
      prompt += `**Beta Agent评估：**\n${discussion.betaMessage}\n\n`;
      prompt += `**生成关键词：** ${discussion.keywords.join(', ')}\n\n`;
    });
  }

  // 添加搜索结果
  if (searchResults.length > 0) {
    prompt += `## 信息收集结果\n\n`;
    searchResults.forEach((result, index) => {
      if (result.state === "completed" && result.learning) {
        prompt += `### ${index + 1}. ${result.query}\n\n`;
        prompt += `**研究目标：** ${result.researchGoal}\n\n`;
        prompt += `**学习内容：**\n${result.learning}\n\n`;
        
        if (result.sources && result.sources.length > 0) {
          prompt += `**信息来源：**\n`;
          result.sources.forEach((source, sourceIndex) => {
            prompt += `${sourceIndex + 1}. [${source.title || source.url}](${source.url})\n`;
          });
          prompt += `\n`;
        }
      }
    });
  }

  prompt += `## 撰写要求

请基于以上信息为章节"${chapterTitle}"撰写一份专业的研究报告，要求：

1. **结构清晰**：包含引言、主要发现、详细分析、结论等部分
2. **内容充实**：充分利用收集到的信息，确保内容丰富和准确
3. **逻辑严密**：确保各部分内容逻辑连贯，论证有力
4. **专业性强**：使用专业术语，保持学术写作风格
5. **引用规范**：适当引用信息来源，增强可信度

请用中文撰写，字数控制在1000-2000字之间。`;

  return prompt;
}

/**
 * 生成最终合成报告的提示词
 */
function getFinalReportPrompt(
  originalPlan: string,
  chapterReports: { title: string; report: string }[],
  requirement?: string
): string {
  let prompt = `你是一名资深的研究报告编辑，需要将多个章节报告合成为一份完整的研究报告。

## 原始研究计划
${originalPlan}

## 章节报告内容

`;

  chapterReports.forEach((chapter, index) => {
    prompt += `### ${index + 1}. ${chapter.title}\n\n`;
    prompt += `${chapter.report}\n\n`;
    prompt += `---\n\n`;
  });

  if (requirement) {
    prompt += `## 特殊要求\n${requirement}\n\n`;
  }

  prompt += `## 合成要求

请将以上章节报告合成为一份完整、专业的研究报告，要求：

1. **整体结构**：
   - 标题和摘要
   - 引言/背景
   - 主体内容（基于章节报告重新组织）
   - 结论和建议
   - 参考文献（如有）

2. **内容整合**：
   - 消除章节间的重复内容
   - 确保各部分内容衔接自然
   - 保持逻辑一致性和完整性

3. **质量提升**：
   - 优化语言表达，提高可读性
   - 增强论证的说服力
   - 确保专业性和准确性

4. **格式规范**：
   - 使用Markdown格式
   - 合理使用标题层级
   - 适当添加列表和表格

请用中文撰写，确保报告的专业性和完整性。`;

  return prompt;
}

/**
 * 生成章节报告
 */
export async function generateChapterReport(
  chapter: Chapter,
  searchResults: SearchTask[],
  discussions: AgentDiscussion[],
  modelProvider: any
): Promise<string> {
  try {
    const prompt = getChapterReportPrompt(
      chapter.title,
      chapter.goal,
      searchResults.filter(r => r.state === "completed"),
      discussions.sort((a, b) => a.round - b.round)
    );

    const result = await streamText({
      model: modelProvider,
      prompt,
    });

    let report = '';
    for await (const chunk of result.textStream) {
      report += chunk;
    }

    return report;
  } catch (error) {
    throw new Error(`生成章节报告失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 合成最终报告
 */
export async function generateFinalReport(
  originalPlan: string,
  chapters: Chapter[],
  modelProvider: any,
  requirement?: string
): Promise<string> {
  try {
    const chapterReports = chapters
      .filter(chapter => chapter.chapterReport && chapter.chapterReport.trim())
      .sort((a, b) => a.order - b.order)
      .map(chapter => ({
        title: chapter.title,
        report: chapter.chapterReport,
      }));

    if (chapterReports.length === 0) {
      throw new Error("没有可用的章节报告");
    }

    const prompt = getFinalReportPrompt(originalPlan, chapterReports, requirement);

    const result = await streamText({
      model: modelProvider,
      prompt,
    });

    let finalReport = '';
    for await (const chunk of result.textStream) {
      finalReport += chunk;
    }

    return finalReport;
  } catch (error) {
    throw new Error(`生成最终报告失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

/**
 * 获取章节报告统计信息
 */
export function getChapterReportStats(chapters: Chapter[]): {
  totalChapters: number;
  completedReports: number;
  totalWords: number;
  averageWords: number;
} {
  const completedReports = chapters.filter(
    chapter => chapter.chapterReport && chapter.chapterReport.trim()
  );
  
  const totalWords = completedReports.reduce((sum, chapter) => {
    return sum + (chapter.chapterReport?.length || 0);
  }, 0);

  return {
    totalChapters: chapters.length,
    completedReports: completedReports.length,
    totalWords,
    averageWords: completedReports.length > 0 ? Math.round(totalWords / completedReports.length) : 0,
  };
}

/**
 * 验证章节报告完整性
 */
export function validateChapterReports(chapters: Chapter[]): {
  valid: boolean;
  missingReports: string[];
  errors: string[];
} {
  const errors: string[] = [];
  const missingReports: string[] = [];

  chapters.forEach(chapter => {
    if (!chapter.chapterReport || !chapter.chapterReport.trim()) {
      missingReports.push(chapter.title);
    } else if (chapter.chapterReport.length < 100) {
      errors.push(`章节"${chapter.title}"的报告内容过短`);
    }
  });

  if (chapters.length === 0) {
    errors.push("没有章节数据");
  }

  return {
    valid: errors.length === 0 && missingReports.length === 0,
    missingReports,
    errors,
  };
}
